# RAG 服务迁移指南

## 概述

本指南帮助您从旧版 RAG 服务迁移到重构后的新版本。新版本提供了更好的架构设计、性能优化和功能扩展。

## 迁移概览

### 主要变更

1. **API 端点变更**: 新增 v2 版本 API
2. **服务架构重构**: 模块化设计，职责分离
3. **配置管理优化**: 统一配置，支持环境变量
4. **错误处理改进**: 更详细的错误信息
5. **性能优化**: 改进的缓存和连接管理

### 兼容性

- ✅ **向后兼容**: 旧版 API 仍然可用（标记为 deprecated）
- ✅ **数据兼容**: 现有数据无需迁移
- ✅ **配置兼容**: 现有配置继续有效
- ⚠️ **逐步迁移**: 建议逐步迁移到新版 API

## API 迁移

### 1. 文档管理 API

#### 旧版本 → 新版本

| 功能     | 旧版 API                                | 新版 API                                   | 状态            |
| -------- | --------------------------------------- | ------------------------------------------ | --------------- |
| 建立索引 | `POST /api/v1/rag/index`                | `POST /api/v1/rag/v2/index`                | ✅ 可用         |
| 获取集合 | `GET /api/v1/rag/collections`           | `GET /api/v1/rag/v2/collections`           | ✅ 可用         |
| 删除集合 | `DELETE /api/v1/rag/collections/{name}` | `DELETE /api/v1/rag/v2/collections/{name}` | ✅ 可用         |
| 查询文档 | `POST /api/v1/rag/query`                | ❌ 已移除                                  | ⚠️ 使用对话 API |

#### 迁移示例

**旧版本**:

```bash
curl -X POST "http://localhost:8000/api/v1/rag/index" \
  -F "file=@document.md" \
  -F "course_id=python_course"
```

**新版本**:

```bash
curl -X POST "http://localhost:8000/api/v1/rag/v2/index" \
  -F "file=@document.md" \
  -F "course_id=python_course" \
  -F "course_material_id=chapter1" \
  -F "course_material_name=Python基础"
```

**变更说明**:

- 新增必需参数: `course_material_id`, `course_material_name`
- 响应格式保持兼容

### 2. 对话管理 API

#### 旧版本 → 新版本

| 功能     | 旧版 API                                 | 新版 API                                            | 状态    |
| -------- | ---------------------------------------- | --------------------------------------------------- | ------- |
| 智能聊天 | `POST /api/v1/chat/intelligent`          | `POST /api/v1/conversation/v2/chat`                 | ✅ 可用 |
| 获取引擎 | `GET /api/v1/chat/engines`               | `GET /api/v1/conversation/v2/engines`               | ✅ 可用 |
| 清除对话 | `DELETE /api/v1/chat/conversations/{id}` | `DELETE /api/v1/conversation/v2/conversations/{id}` | ✅ 可用 |

#### 迁移示例

**旧版本**:

```bash
curl -X POST "http://localhost:8000/api/v1/chat/intelligent" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "user_123",
    "question": "什么是Python？",
    "chat_engine_type": "condense_plus_context",
    "course_id": "python_course"
  }'
```

**新版本**:

```bash
curl -X POST "http://localhost:8000/api/v1/conversation/v2/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "user_123",
    "question": "什么是Python？",
    "chat_engine_type": "condense_plus_context",
    "course_id": "python_course"
  }'
```

**变更说明**:

- 请求格式完全兼容
- 响应格式保持一致
- 新增更多配置选项

## 配置迁移

### 环境变量支持

新版本支持通过环境变量覆盖默认配置：

```bash
# RAG相关配置
export RAG_LLM_MODEL=gpt-4
export RAG_CHUNK_SIZE=1024
export RAG_REDIS_URL=redis://localhost:6379
export RAG_REDIS_TTL=3600

# 启动服务
python -m uvicorn app.main:app --reload
```

### 配置文件

现有的配置文件无需修改，新版本完全兼容：

```python
# app/core/config.py
class Settings(BaseSettings):
    # 现有配置保持不变
    api_key: str
    base_url: str
    # ... 其他配置
```

## 代码迁移

### 1. Python 客户端

#### 旧版本代码

```python
import requests

# 文档索引
response = requests.post(
    "http://localhost:8000/api/v1/rag/index",
    files={"file": open("doc.md", "rb")},
    data={"course_id": "python_course"}
)

# 智能聊天
response = requests.post(
    "http://localhost:8000/api/v1/chat/intelligent",
    json={
        "conversation_id": "user_123",
        "question": "什么是Python？",
        "chat_engine_type": "condense_plus_context",
        "course_id": "python_course"
    }
)
```

#### 新版本代码

```python
import requests

# 文档索引 (新增必需参数)
response = requests.post(
    "http://localhost:8000/api/v1/rag/v2/index",
    files={"file": open("doc.md", "rb")},
    data={
        "course_id": "python_course",
        "course_material_id": "chapter1",  # 新增
        "course_material_name": "Python基础"  # 新增
    }
)

# 智能聊天 (端点变更)
response = requests.post(
    "http://localhost:8000/api/v1/conversation/v2/chat",
    json={
        "conversation_id": "user_123",
        "question": "什么是Python？",
        "chat_engine_type": "condense_plus_context",
        "course_id": "python_course"
    }
)
```

### 2. JavaScript/前端

#### 旧版本代码

```javascript
// 文档上传
const formData = new FormData();
formData.append("file", file);
formData.append("course_id", "python_course");

fetch("/api/v1/rag/index", {
  method: "POST",
  body: formData,
});

// 聊天
fetch("/api/v1/chat/intelligent", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    conversation_id: "user_123",
    question: "什么是Python？",
    chat_engine_type: "condense_plus_context",
    course_id: "python_course",
  }),
});
```

#### 新版本代码

```javascript
// 文档上传 (新增参数)
const formData = new FormData();
formData.append("file", file);
formData.append("course_id", "python_course");
formData.append("course_material_id", "chapter1"); // 新增
formData.append("course_material_name", "Python基础"); // 新增

fetch("/api/v1/rag/v2/index", {
  // 端点变更
  method: "POST",
  body: formData,
});

// 聊天 (端点变更)
fetch("/api/v1/conversation/v2/chat", {
  // 端点变更
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    conversation_id: "user_123",
    question: "什么是Python？",
    chat_engine_type: "condense_plus_context",
    course_id: "python_course",
  }),
});
```

## 迁移步骤

### 阶段 1: 准备阶段

1. **备份数据**: 备份现有的向量数据库和 Redis 数据
2. **测试环境**: 在测试环境中部署新版本
3. **功能验证**: 验证所有功能正常工作

### 阶段 2: 并行运行

1. **部署新版本**: 在生产环境部署新版本
2. **保持旧 API**: 旧版 API 继续提供服务
3. **监控日志**: 监控新版本的运行状况

### 阶段 3: 逐步迁移

1. **更新客户端**: 逐步更新客户端代码使用新 API
2. **功能测试**: 确保新 API 功能正常
3. **性能监控**: 监控性能指标

### 阶段 4: 完成迁移

1. **停用旧 API**: 在确认新 API 稳定后停用旧 API
2. **清理代码**: 移除旧版本相关代码
3. **文档更新**: 更新相关文档

## 常见问题

### Q1: 旧版 API 什么时候会被移除？

A: 旧版 API 将在新版本稳定运行 3 个月后标记为 deprecated，6 个月后正式移除。

### Q2: 数据需要迁移吗？

A: 不需要。新版本完全兼容现有的数据格式。

### Q3: 配置文件需要修改吗？

A: 不需要。现有配置文件继续有效，但建议使用新的环境变量配置。

### Q4: 性能会有提升吗？

A: 是的。新版本在架构和性能方面都有优化，详见性能测试报告。

### Q5: 如何回滚到旧版本？

A: 如果遇到问题，可以通过配置开关快速回滚到旧版本 API。

## 支持和帮助

### 技术支持

- **文档**: 查看 `API_v2_文档.md`
- **示例**: 参考 `test_frontend_api.py`
- **测试**: 运行端到端测试验证功能

### 迁移检查清单

- [ ] 备份现有数据
- [ ] 在测试环境验证新版本
- [ ] 更新客户端代码
- [ ] 测试所有功能
- [ ] 监控性能指标
- [ ] 更新文档
- [ ] 培训团队成员

## 部署文档

### 生产环境部署

详细的部署指南请参考 `部署文档.md`，包括：

- Docker 容器化部署
- 环境变量配置
- 负载均衡设置
- 监控和日志配置
- 备份和恢复策略

---

**迁移建议**: 建议采用渐进式迁移策略，先在非关键功能上使用新 API，确认稳定后再全面迁移。
