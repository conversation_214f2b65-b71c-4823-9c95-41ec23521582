# RAG服务部署文档

## 概述

本文档提供RAG服务重构后版本的完整部署指南，包括开发环境、测试环境和生产环境的部署方案。

## 系统要求

### 硬件要求

**最低配置**:
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB 可用空间
- 网络: 稳定的互联网连接

**推荐配置**:
- CPU: 4核心或更多
- 内存: 8GB RAM或更多
- 存储: 50GB SSD
- 网络: 高速互联网连接

### 软件要求

- **Python**: 3.11+
- **Redis**: 6.0+
- **Qdrant**: 1.0+
- **Docker**: 20.10+ (可选)
- **Docker Compose**: 2.0+ (可选)

## 环境配置

### 环境变量

创建 `.env` 文件配置环境变量：

```bash
# 基础配置
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# RAG服务配置
RAG_LLM_MODEL=gpt-4o-mini
RAG_EMBED_MODEL=text-embedding-3-small
RAG_CHUNK_SIZE=512
RAG_CHUNK_OVERLAP=50
RAG_REDIS_URL=redis://localhost:6379
RAG_REDIS_TTL=3600

# Qdrant配置
QDRANT_HOST=localhost
QDRANT_PORT=6334
QDRANT_COLLECTION_NAME=course_materials

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=.md,.txt
```

### 依赖服务

#### 1. Redis安装

**Docker方式**:
```bash
docker run -d \
  --name redis \
  -p 6379:6379 \
  redis:7-alpine
```

**本地安装**:
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis
```

#### 2. Qdrant安装

**Docker方式**:
```bash
docker run -d \
  --name qdrant \
  -p 6333:6333 \
  -p 6334:6334 \
  -v qdrant_storage:/qdrant/storage \
  qdrant/qdrant
```

**本地安装**:
```bash
# 下载并运行Qdrant
wget https://github.com/qdrant/qdrant/releases/latest/download/qdrant-x86_64-unknown-linux-gnu.tar.gz
tar -xzf qdrant-x86_64-unknown-linux-gnu.tar.gz
./qdrant
```

## 部署方案

### 方案1: 开发环境部署

#### 1. 克隆代码

```bash
git clone <repository_url>
cd rwai_fastapi
```

#### 2. 创建虚拟环境

```bash
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows
```

#### 3. 安装依赖

```bash
pip install -r requirements.txt
```

#### 4. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入正确的配置
```

#### 5. 启动服务

```bash
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 方案2: Docker容器化部署

#### 1. 创建Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p data/uploads data/outputs logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - REDIS_URL=redis://redis:6379
      - QDRANT_HOST=qdrant
    depends_on:
      - redis
      - qdrant
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped

volumes:
  redis_data:
  qdrant_data:
```

#### 3. 启动服务

```bash
docker-compose up -d
```

### 方案3: 生产环境部署

#### 1. 使用Nginx反向代理

**nginx.conf**:
```nginx
upstream rag_backend {
    server 127.0.0.1:8000;
    # 可以添加多个实例实现负载均衡
    # server 127.0.0.1:8001;
    # server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name your-domain.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # 文件上传大小限制
    client_max_body_size 50M;

    # 代理到后端服务
    location / {
        proxy_pass http://rag_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 健康检查
    location /health {
        proxy_pass http://rag_backend/health;
        access_log off;
    }
}
```

#### 2. 使用Systemd服务

**rag-service.service**:
```ini
[Unit]
Description=RAG Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/rag-service
Environment=PATH=/opt/rag-service/.venv/bin
ExecStart=/opt/rag-service/.venv/bin/python -m uvicorn app.main:app --host 127.0.0.1 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable rag-service
sudo systemctl start rag-service
sudo systemctl status rag-service
```

#### 3. 使用Gunicorn

**gunicorn.conf.py**:
```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 60
keepalive = 5
preload_app = True
```

启动命令：
```bash
gunicorn app.main:app -c gunicorn.conf.py
```

## 监控和日志

### 1. 日志配置

应用日志配置在 `app/core/logging.py` 中：

```python
# 生产环境日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s",
        },
    },
    "handlers": {
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/app.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "default",
        },
    },
    "root": {
        "level": "INFO",
        "handlers": ["file"],
    },
}
```

### 2. 健康检查

应用提供多个健康检查端点：

- `GET /health` - 基础健康检查
- `GET /api/v1/rag/v2/health` - RAG服务健康检查
- `GET /api/v1/conversation/v2/health` - 对话服务健康检查

### 3. 监控指标

建议监控以下指标：

- **响应时间**: API响应时间
- **错误率**: 4xx和5xx错误比例
- **吞吐量**: 每秒请求数
- **资源使用**: CPU、内存、磁盘使用率
- **依赖服务**: Redis和Qdrant连接状态

## 备份和恢复

### 1. 数据备份

**Redis备份**:
```bash
# 创建备份
redis-cli BGSAVE

# 复制备份文件
cp /var/lib/redis/dump.rdb /backup/redis-$(date +%Y%m%d).rdb
```

**Qdrant备份**:
```bash
# 停止Qdrant服务
sudo systemctl stop qdrant

# 备份数据目录
tar -czf /backup/qdrant-$(date +%Y%m%d).tar.gz /var/lib/qdrant/

# 重启服务
sudo systemctl start qdrant
```

### 2. 数据恢复

**Redis恢复**:
```bash
# 停止Redis
sudo systemctl stop redis

# 恢复备份
cp /backup/redis-20250820.rdb /var/lib/redis/dump.rdb

# 重启Redis
sudo systemctl start redis
```

**Qdrant恢复**:
```bash
# 停止Qdrant
sudo systemctl stop qdrant

# 恢复数据
tar -xzf /backup/qdrant-20250820.tar.gz -C /

# 重启服务
sudo systemctl start qdrant
```

## 安全配置

### 1. 防火墙设置

```bash
# 只允许必要的端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. API密钥管理

```bash
# 使用强密钥
export OPENAI_API_KEY="sk-..."

# 定期轮换密钥
# 在环境变量中配置，不要硬编码
```

### 3. 访问控制

考虑添加API访问控制：

- IP白名单
- API密钥认证
- 请求频率限制
- CORS配置

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 验证环境变量配置
   - 查看日志文件

2. **Redis连接失败**
   - 确认Redis服务运行状态
   - 检查网络连接
   - 验证连接字符串

3. **Qdrant连接失败**
   - 确认Qdrant服务运行状态
   - 检查端口配置
   - 验证数据目录权限

4. **API响应慢**
   - 检查系统资源使用
   - 优化数据库查询
   - 调整并发配置

### 日志分析

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 查看访问日志
tail -f /var/log/nginx/access.log
```

---

**部署建议**: 建议先在测试环境完整验证部署流程，确认无误后再部署到生产环境。
