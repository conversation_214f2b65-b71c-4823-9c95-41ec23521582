# 下一步操作指南

## 当前中断位置

**文件：** `app/main.py`
**位置：** 第48-57行，`lifespan` 函数中的配置验证部分之后
**任务：** 添加RAG配置管理器初始化代码

## 立即需要完成的代码修改

### 1. 完成 app/main.py 修改

在 `app/main.py` 的第57行之后添加以下代码：

```python
    # 初始化RAG配置管理器
    try:
        logger.info("🔧 初始化RAG配置管理器...")
        initialize_rag_config(settings)
        logger.info("✅ RAG配置管理器初始化完成")
    except Exception as e:
        logger.error(f"❌ RAG配置管理器初始化失败: {str(e)}")
        raise
```

**具体操作：**
```bash
# 使用str-replace-editor工具修改文件
# 在第57行 "raise" 之后添加上述代码块
```

## 后续任务清单

### 阶段四剩余任务

#### 1. 完整功能验证测试
**文件位置：** `tests/test_end_to_end.py` (需创建)
**内容：**
- 端到端测试：文档索引 → 对话查询 → 结果返回
- 测试新旧API兼容性
- 验证环境变量配置覆盖

#### 2. 前端功能测试
**操作步骤：**
1. 启动后端服务：`python -m uvicorn app.main:app --reload`
2. 打开前端界面
3. 测试功能：
   - 文档上传和索引建立
   - 智能对话（两种模式）
   - 课程材料管理
   - 数据清理功能

#### 3. 性能验证
**测试项目：**
- 索引建立速度对比
- 对话响应时间对比
- 内存使用情况
- 并发处理能力

## 测试验证命令

### 运行所有测试
```bash
# 激活虚拟环境
.venv\Scripts\activate

# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_rag_settings.py -v
python -m pytest tests/test_rag_services_integration.py -v
python -m pytest tests/test_api_v2_integration.py -v
```

### 启动服务器
```bash
# 开发模式启动
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或者直接运行
python app/main.py
```

## 环境变量测试

### 测试RAG配置覆盖
```bash
# 设置环境变量
set RAG_LLM_MODEL=gpt-4
set RAG_CHUNK_SIZE=1024
set RAG_REDIS_URL=redis://localhost:6380

# 启动服务验证配置
python app/main.py
```

## API测试示例

### 测试新版本API

#### 1. 文档索引API v2
```bash
curl -X POST "http://localhost:8000/api/v1/rag/v2/index" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test.md" \
  -F "course_id=test_course" \
  -F "course_material_id=test_material" \
  -F "course_material_name=Test Material"
```

#### 2. 对话API v2
```bash
curl -X POST "http://localhost:8000/api/v1/conversation/v2/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "test_conv",
    "question": "什么是Python？",
    "chat_engine_type": "condense_plus_context",
    "course_id": "test_course"
  }'
```

#### 3. 健康检查
```bash
curl "http://localhost:8000/api/v1/rag/v2/health"
curl "http://localhost:8000/api/v1/conversation/v2/health"
```

## 问题排查指南

### 常见问题及解决方案

#### 1. 导入错误
**症状：** `ModuleNotFoundError`
**解决：** 检查Python路径和虚拟环境激活

#### 2. 配置加载失败
**症状：** RAG配置管理器初始化失败
**解决：** 检查环境变量格式和API密钥配置

#### 3. 数据库连接问题
**症状：** Qdrant连接失败
**解决：** 确保Qdrant服务运行在localhost:6334

#### 4. Redis连接问题
**症状：** 对话内存创建失败
**解决：** 确保Redis服务运行在localhost:6379

## 验收标准

### 功能验收
- [ ] 所有单元测试通过
- [ ] 所有集成测试通过
- [ ] 新API正常工作
- [ ] 旧API仍然可用（带deprecated警告）
- [ ] 前端所有功能正常

### 性能验收
- [ ] 响应时间不超过原系统的120%
- [ ] 内存使用合理
- [ ] 无内存泄漏

### 配置验收
- [ ] 环境变量正确覆盖默认配置
- [ ] 配置错误有清晰的错误信息
- [ ] 服务启动日志完整

## 完成标志

当以下所有项目完成时，重构工作即告完成：

1. ✅ 基础设施重构
2. ✅ 服务重构  
3. ✅ API路由更新
4. 🔄 配置增强和测试
   - [ ] 环境变量支持
   - [ ] 完整功能验证
   - [ ] 前端测试
   - [ ] 性能验证

## 紧急联系信息

**重要文件位置：**
- 重构计划：`dev_docs/重构计划.md`
- 进度报告：`dev_docs/重构进度报告.md`
- 当前指南：`dev_docs/下一步操作指南.md`

**关键代码位置：**
- RAG配置：`app/services/rag/rag_settings.py`
- 文档索引：`app/services/rag/document_indexing_service.py`
- 对话服务：`app/services/rag/conversation_service.py`
- API v2：`app/api/v1/rag_v2.py`, `app/api/v1/conversation_v2.py`
