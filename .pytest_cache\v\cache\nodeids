["tests/test_api_v2_integration.py::TestAPIV2Integration::test_api_version_consistency", "tests/test_api_v2_integration.py::TestAPIV2Integration::test_full_workflow_index_and_chat", "tests/test_api_v2_integration.py::TestConversationV2API::test_chat_empty_conversation_id", "tests/test_api_v2_integration.py::TestConversationV2API::test_chat_empty_question", "tests/test_api_v2_integration.py::TestConversationV2API::test_clear_conversation", "tests/test_api_v2_integration.py::TestConversationV2API::test_clear_conversation_empty_id", "tests/test_api_v2_integration.py::TestConversationV2API::test_conversation_health_check", "tests/test_api_v2_integration.py::TestConversationV2API::test_get_available_engines", "tests/test_api_v2_integration.py::TestConversationV2API::test_get_conversation_config", "tests/test_api_v2_integration.py::TestConversationV2API::test_get_conversation_status", "tests/test_api_v2_integration.py::TestConversationV2API::test_intelligent_chat_condense_plus_context_engine", "tests/test_api_v2_integration.py::TestConversationV2API::test_intelligent_chat_simple_engine", "tests/test_api_v2_integration.py::TestRAGV2API::test_build_index_invalid_file_type", "tests/test_api_v2_integration.py::TestRAGV2API::test_build_index_success", "tests/test_api_v2_integration.py::TestRAGV2API::test_count_documents", "tests/test_api_v2_integration.py::TestRAGV2API::test_delete_collection", "tests/test_api_v2_integration.py::TestRAGV2API::test_delete_documents_by_course", "tests/test_api_v2_integration.py::TestRAGV2API::test_delete_documents_by_material", "tests/test_api_v2_integration.py::TestRAGV2API::test_get_collection_info", "tests/test_api_v2_integration.py::TestRAGV2API::test_get_collection_info_not_found", "tests/test_api_v2_integration.py::TestRAGV2API::test_get_collections", "tests/test_api_v2_integration.py::TestRAGV2API::test_health_check", "tests/test_end_to_end.py::TestEndToEnd::test_api_compatibility", "tests/test_end_to_end.py::TestEndToEnd::test_complete_workflow_new_api", "tests/test_end_to_end.py::TestEndToEnd::test_error_handling", "tests/test_end_to_end.py::TestEnvironmentVariables::test_environment_variable_override", "tests/test_outline.py::TestOutlineAPI::test_generate_outline_success", "tests/test_outline.py::TestOutlineAPI::test_get_metrics", "tests/test_outline.py::TestOutlineAPI::test_get_nonexistent_task", "tests/test_outline.py::TestOutlineAPI::test_get_outline_file_empty_params", "tests/test_outline.py::TestOutlineAPI::test_get_outline_file_not_found_course", "tests/test_outline.py::TestOutlineAPI::test_get_outline_file_not_found_material", "tests/test_outline.py::TestOutlineAPI::test_get_outline_file_success", "tests/test_outline.py::TestOutlineAPI::test_health_check", "tests/test_outline.py::TestOutlineAPI::test_list_tasks", "tests/test_outline.py::TestOutlineAPI::test_root_endpoint", "tests/test_outline.py::TestOutlineAPI::test_upload_empty_file", "tests/test_outline.py::TestOutlineAPI::test_upload_invalid_file_type", "tests/test_rag.py::TestQdrantRepository::test_create_collection", "tests/test_rag.py::TestQdrantRepository::test_init", "tests/test_rag.py::TestQdrantRepository::test_search_points", "tests/test_rag.py::TestRAGSchemas::test_chat_mode_enum", "tests/test_rag.py::TestRAGSchemas::test_document_metadata", "tests/test_rag.py::TestRAGSchemas::test_query_request_defaults", "tests/test_rag.py::TestRAGService::test_build_index_success", "tests/test_rag.py::TestRAGService::test_init", "tests/test_rag.py::TestRAGService::test_query_chat_mode", "tests/test_rag.py::TestRAGService::test_query_retrieval_mode", "tests/test_rag.py::TestRAGService::test_update_chat_memory", "tests/test_rag_services_integration.py::TestConversationService::test_chat_condense_plus_context_engine", "tests/test_rag_services_integration.py::TestConversationService::test_chat_simple_engine", "tests/test_rag_services_integration.py::TestConversationService::test_clear_conversation", "tests/test_rag_services_integration.py::TestDocumentIndexingService::test_build_index_success", "tests/test_rag_services_integration.py::TestDocumentIndexingService::test_delete_documents_by_course", "tests/test_rag_services_integration.py::TestDocumentIndexingService::test_get_collections", "tests/test_rag_services_integration.py::TestServicesIntegration::test_document_indexing_and_conversation_workflow", "tests/test_rag_services_integration.py::TestServicesIntegration::test_service_status_methods", "tests/test_rag_settings.py::TestModuleFunctions::test_initialize_rag_config", "tests/test_rag_settings.py::TestRAGConfigManager::test_get_config_methods", "tests/test_rag_settings.py::TestRAGConfigManager::test_get_settings_summary", "tests/test_rag_settings.py::TestRAGConfigManager::test_get_settings_summary_not_initialized", "tests/test_rag_settings.py::TestRAGConfigManager::test_get_text_splitter", "tests/test_rag_settings.py::TestRAGConfigManager::test_get_text_splitter_not_initialized", "tests/test_rag_settings.py::TestRAGConfigManager::test_initialize_success", "tests/test_rag_settings.py::TestRAGConfigManager::test_initialize_without_app_settings", "tests/test_rag_settings.py::TestRAGConfigManager::test_reload_settings", "tests/test_rag_settings.py::TestRAGConfigManager::test_singleton_pattern", "tests/test_rag_settings.py::TestRAGSettings::test_default_values", "tests/test_rag_settings.py::TestRAGSettings::test_environment_variable_override"]