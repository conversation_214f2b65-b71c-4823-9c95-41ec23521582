# 基于 Redis 共享内存的智能聊天系统开发文档

## 概述

本文档说明如何基于 `llama_index_shared_memory_redis_storage.ipynb` 中的成功实现，在 FastAPI 项目中集成智能聊天系统。该系统支持：

- **Redis 共享内存**：多会话间的对话记忆持久化
- **动态过滤**：基于 course_id 或 course_material_id 的精确检索
- **双引擎模式**：condense_plus_context（检索增强）和 simple（直接对话）
- **会话管理**：基于 conversation_id 的独立对话管理

## 核心功能需求

### 1. 用户输入参数

每次对话需要用户提供以下参数：

- `conversation_id`：对话会话 ID，用作 Redis 存储键
- `course_id`：课程 ID（与 course_material_id 二选一）
- `course_material_id`：课程材料 ID（与 course_id 二选一）
- `chat_engine_type`：聊天引擎类型（"condense_plus_context" 或 "simple"）
- `question`：用户问题

### 2. 动态过滤逻辑

- 如果提供 `course_id`，检索时只匹配该课程的文本块
- 如果提供 `course_material_id`，检索时只匹配该材料的文本块
- `course_id` 和 `course_material_id` 只能存在一个，如果同时存在则优先使用 `course_id`

### 3. 聊天引擎模式

- **condense_plus_context**：使用 `index.as_chat_engine()` 的 condense_plus_context 模式
- **simple**：使用 `SimpleChatEngine` 进行直接对话

## 技术架构

### 核心组件

1. **Redis ChatStore**

   - 存储地址：`redis://localhost:6379`
   - TTL：3600 秒（1 小时）
   - 用途：持久化对话历史

2. **ChatSummaryMemoryBuffer**

   - Token 限制：4000
   - 摘要提示词：自定义的对话记忆助理提示
   - 存储键：用户提供的 conversation_id

3. **LlamaIndex 配置**
   - LLM：GPT-4o-mini，温度 0.1
   - 嵌入模型：text-embedding-3-small
   - 向量数据库：Qdrant（localhost:6334）

### 提示词配置

#### 问题压缩提示词（condense_prompt）

用于将用户问题结合历史对话改写为独立的检索查询。

#### 上下文整合提示词（context_prompt）

定义 AI 助手"文文"的角色和回答风格，整合检索到的文档内容。

#### 摘要提示词（summarize_prompt）

用于对话历史的智能摘要，控制内存使用。

## 实现步骤

### 第一阶段：后端服务实现

#### 1. 创建聊天服务类

需要创建新的 `ChatService` 类，集成以下功能：

- Redis ChatStore 连接管理
- 动态 Memory 创建（基于 conversation_id）
- 双引擎模式支持
- 动态过滤器配置

#### 2. 扩展数据模型

在现有 `schemas/rag.py` 基础上添加：

- `ChatRequest`：包含所有用户输入参数
- `ChatResponse`：返回答案、来源和更新的记忆
- `ChatEngineType`：枚举类型定义

#### 3. 新增 API 端点

创建新的聊天 API 端点：

- `POST /api/v1/chat`：主要聊天接口
- 支持所有必需参数
- 实现动态过滤逻辑
- 返回详细的响应信息

### 第二阶段：前端界面更新

#### 1. 聊天界面改进

在现有的 RAG 聊天页面基础上添加：

- conversation_id 输入框
- course_id 输入框（可选）
- course_material_id 输入框（可选）
- chat_engine_type 选择器
- 互斥逻辑处理（course_id 和 course_material_id）

#### 2. 用户体验优化

- 参数验证和提示
- 会话历史显示
- 引擎模式切换说明
- 错误处理和用户反馈

### 第三阶段：集成测试

#### 1. 功能验证

- Redis 连接和数据持久化
- 动态过滤准确性
- 双引擎模式切换
- 会话隔离效果

#### 2. 性能测试

- 并发会话处理
- 内存使用优化
- 响应时间监控

## 关键技术细节

### Redis 内存管理

- 每个 conversation_id 对应独立的存储空间
- 自动 TTL 过期清理
- 支持多用户并发访问

### 动态过滤实现

使用 LlamaIndex 的 MetadataFilter：

```python
# 示例：基于 course_id 过滤
filters = MetadataFilters(filters=[
    MetadataFilter(key="course_id", value=course_id, operator=FilterOperator.EQ)
])
```

### 引擎配置差异

- **condense_plus_context**：需要配置 condense_prompt 和 context_prompt
- **simple**：只需要 system_prompt 和 memory

## 预期效果

实现后的系统将提供：

1. **智能对话**：基于文档内容的准确回答
2. **记忆持久化**：跨会话的对话历史保持
3. **精确检索**：基于课程或材料的定向搜索
4. **灵活模式**：根据需求选择检索或直接对话
5. **多用户支持**：独立的会话管理和隔离

## 实现状态

### ✅ 已完成

1. **后端服务实现**

   - ✅ 创建 `ChatService` 类，集成 Redis ChatStore 和双引擎模式
   - ✅ 扩展数据模型，添加 `ChatRequest` 和 `ChatResponse`
   - ✅ 新增 `/api/v1/chat/` API 端点，支持所有必需参数
   - ✅ 实现动态过滤逻辑（course_id 和 course_material_id）

2. **前端界面更新**

   - ✅ 更新 RAG 聊天页面，添加所有必需输入字段
   - ✅ 实现参数验证和互斥逻辑
   - ✅ 添加会话管理和引擎切换功能
   - ✅ 创建 `ChatAPI` 客户端

3. **技术集成**
   - ✅ 集成 Redis 共享内存存储
   - ✅ 配置 LlamaIndex 全局设置
   - ✅ 实现与 notebook 一致的提示词配置

### 🔄 测试和验证

运行测试脚本验证实现：

```bash
# 1. 安装新增依赖
pip install llama-index-storage-chat-store-redis redis

# 2. 确保外部服务运行
# Redis: localhost:6379
# Qdrant: localhost:6334

# 3. 运行测试脚本
python test_chat_implementation.py
```

### 🚀 部署步骤

1. **启动服务**

   ```bash
   # 启动FastAPI服务器
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **访问前端**

   - 打开 `frontend/index.html`
   - 点击"智能问答"菜单
   - 填写必需参数进行测试

3. **验证功能**
   - 测试不同的 conversation_id 会话隔离
   - 验证 course_id 和 course_material_id 过滤效果
   - 切换 condense_plus_context 和 simple 引擎模式

## 使用示例

### 检索增强模式示例

```json
{
  "conversation_id": "user123_session001",
  "course_material_id": "material_红楼梦第一章",
  "chat_engine_type": "condense_plus_context",
  "question": "王夫人的娘家是哪个家族？"
}
```

### 直接对话模式示例

```json
{
  "conversation_id": "user123_session001",
  "chat_engine_type": "simple",
  "question": "请总结一下我们刚才的对话"
}
```

## 故障排除

### 常见问题

1. **Redis 连接失败**

   - 确保 Redis 服务运行在 localhost:6379
   - 检查防火墙设置

2. **Qdrant 连接失败**

   - 确保 Qdrant 服务运行在 localhost:6334
   - 验证集合 `course_materials` 存在

3. **依赖包缺失**
   - 运行 `pip install -r requirements.txt`
   - 特别注意 `llama-index-storage-chat-store-redis`

### 调试技巧

- 查看 FastAPI 日志了解详细错误信息
- 使用 `/api/v1/chat/health` 端点检查服务状态
- 通过 `/docs` 查看 API 文档和测试接口

---

_本文档基于 `llama_index_shared_memory_redis_storage.ipynb` 的成功实现编写，确保技术方案的可行性和稳定性。_
