# RAG 服务重构计划

## 📋 重构目标

基于当前项目的实际情况和最佳实践，对 RAG 相关服务进行重构，实现：

- **职责清晰**：明确分离文档管理和对话管理功能
- **代码复用**：统一 LlamaIndex 配置和提示词管理
- **架构优化**：建立清晰的服务分层和依赖关系
- **配置灵活**：支持环境变量覆盖默认配置

## 🎯 重构思路

### 核心设计原则

1. **领域驱动设计**：按业务领域组织代码结构
2. **单一职责原则**：每个服务专注一个核心功能
3. **依赖注入**：通过构造函数注入依赖，便于测试
4. **配置外部化**：支持环境变量覆盖，提高部署灵活性
5. **提示词集中管理**：统一存放在 `app/prompts` 目录

### 架构分层

```
app/
├── services/
│   ├── rag/                        # RAG 领域服务
│   │   ├── __init__.py
│   │   ├── rag_settings.py         # RAG 配置管理
│   │   ├── document_indexing_service.py  # 文档索引 + 向量存储
│   │   └── conversation_service.py # 对话管理（内存+引擎+聊天）
│   ├── course_material/            # 课程材料领域服务
│   │   ├── __init__.py
│   │   ├── course_material_process_service.py  # 重命名
│   │   └── cleanup_service.py
│   └── legacy/                     # 保留原有服务（逐步迁移）
│       ├── __init__.py
│       ├── rag_service.py          # 标记为 deprecated
│       └── chat_service.py         # 标记为 deprecated
├── prompts/                        # 提示词集中管理
│   ├── condense_question.txt       # 问题压缩提示词
│   ├── context_integration.txt     # 上下文整合提示词
│   ├── chat_summary.txt           # 对话摘要提示词
│   ├── simple_system.txt          # 简单聊天系统提示词
│   ├── outline_generation.txt     # 大纲生成（保留）
│   └── outline_refine.txt         # 大纲精简（保留）
└── api/v1/
    ├── documents.py               # 重命名：文档管理 API
    ├── conversations.py           # 重命名：对话管理 API
    └── course_materials.py       # 保持不变
```

## 🔧 详细重构方案

### 第一阶段：基础设施重构

#### 1. RAG 配置管理器

**文件：** `app/services/rag/rag_settings.py`

**职责：**

- 统一管理 LlamaIndex 全局配置
- 支持环境变量覆盖默认配置
- 提供单例模式，避免重复初始化

**核心功能：**

- LLM 配置（OpenAI）
- 嵌入模型配置
- Redis 连接配置（支持环境变量）
- Qdrant 连接配置

**环境变量支持：**

```env
# Redis 配置
REDIS_URL=redis://localhost:6379
REDIS_TTL=3600

# 其他配置保持现有方式
```

#### 2. 提示词集中管理

**目录：** `app/prompts/`

**管理策略：**

- 删除不再使用的提示词文件
- 重新组织现有提示词，按功能分类
- 服务中直接读取对应的 `.txt` 文件
- 支持运行时动态加载

**提示词文件清单：**

- `condense_question.txt` - 基于 ChatService 中的成功实现
- `context_integration.txt` - 基于 ChatService 中的成功实现
- `chat_summary.txt` - 对话摘要
- `simple_system.txt` - 简单聊天系统提示词
- `outline_generation.txt` - 保留现有
- `outline_refine.txt` - 保留现有

### 第二阶段：服务重构

#### 1. 文档索引服务

**文件：** `app/services/rag/document_indexing_service.py`

**职责：**

- 文档索引建立和管理
- 向量存储操作
- 集合管理

**整合内容：**

- 原 `RAGService` 的索引建立功能
- 向量存储管理逻辑
- Qdrant 操作封装

**核心方法：**

```python
class DocumentIndexingService:
    async def build_index(self, request: IndexRequest) -> IndexResponse
    async def get_collections(self) -> List[CollectionInfo]
    async def delete_collection(self, collection_name: str) -> bool
    async def get_collection_info(self, collection_name: str) -> CollectionInfo
```

#### 2. 对话服务

**文件：** `app/services/rag/conversation_service.py`

**职责：**

- 对话内存管理（Redis）
- 聊天引擎工厂
- 智能聊天处理

**整合内容：**

- 原 `ChatService` 的所有对话功能
- Redis 内存管理
- 双引擎模式支持
- 动态过滤逻辑

**核心组件：**

```python
class ConversationMemoryManager:
    def create_memory(self, conversation_id: str) -> ChatSummaryMemoryBuffer
    def clear_conversation(self, conversation_id: str) -> bool

class ChatEngineFactory:
    def create_engine(self, engine_type: ChatEngineType, ...) -> ChatEngine

class ConversationService:
    async def chat(self, request: ChatRequest) -> ChatResponse
```

#### 3. 课程材料服务重组

**目录：** `app/services/course_material/`

**重命名：**

- `course_process_service.py` → `course_material_process_service.py`
- 移动 `cleanup_service.py` 到同一目录

**更新依赖：**

- 更新对 RAG 服务的引用
- 适配新的服务接口

### 第三阶段：API 路由更新

#### 1. 文档管理 API

**文件：** `app/api/v1/documents.py` (重命名自 `rag.py`)

**端点变更：**

```python
# 保留的端点
POST /api/v1/documents/index          # 文档索引建立
GET  /api/v1/documents/collections    # 获取集合列表
GET  /api/v1/documents/collections/{name}/info  # 集合详情
DELETE /api/v1/documents/collections/{name}     # 删除集合

# 移除的端点
POST /api/v1/rag/query  # 标记为 deprecated，引导使用对话 API
```

#### 2. 对话管理 API

**文件：** `app/api/v1/conversations.py` (重命名自 `chat.py`)

**端点保持：**

```python
POST /api/v1/conversations/chat                    # 智能聊天
GET  /api/v1/conversations/engines                 # 获取引擎列表
GET  /api/v1/conversations/health                  # 健康检查
DELETE /api/v1/conversations/{conversation_id}     # 清除会话
```

#### 3. 前端适配

**影响的文件：**

- `frontend/js/api.js` - 更新 API 端点
- `frontend/js/pages.js` - 适配新的 API 调用

**变更内容：**

- 更新文档管理相关的 API 调用
- 保持对话 API 调用不变（已经是新接口）

### 第四阶段：配置增强

#### 1. 环境变量支持

**新增配置项：**

```python
# app/core/config.py
class Settings(BaseSettings):
    # Redis 配置
    redis_url: str = Field(default="redis://localhost:6379", description="Redis连接URL")
    redis_ttl: int = Field(default=3600, description="Redis数据TTL（秒）")

    # 对话配置
    conversation_token_limit: int = Field(default=4000, description="对话内存Token限制")
    conversation_similarity_top_k: int = Field(default=6, description="对话检索Top-K")
```

#### 2. 向后兼容

**迁移策略：**

- 保留原有 API 端点，标记为 deprecated

## 📊 重构收益

### 1. 架构优化

- **职责清晰**：文档管理 vs 对话管理分离
- **代码复用**：统一的配置和提示词管理
- **依赖解耦**：通过依赖注入降低耦合度

### 2. 维护性提升

- **配置集中**：所有 RAG 相关配置统一管理
- **提示词管理**：集中存放，便于版本控制和更新
- **测试友好**：单一职责便于单元测试

### 3. 部署灵活性

- **环境变量支持**：Redis 等配置可通过环境变量覆盖
- **服务隔离**：不同服务可独立部署和扩展
- **向后兼容**：平滑迁移，不影响现有用户

### 4. 开发效率

- **代码复用**：减少重复代码
- **清晰分层**：新功能开发更容易定位
- **统一接口**：服务间调用更规范

## 🚀 实施计划

### 阶段一：基础设施

1. 创建新的目录结构
2. 实现 `rag_settings.py`
3. 整理提示词文件
4. 单元测试基础组件

### 阶段二：服务重构

1. 实现 `document_indexing_service.py`
2. 实现 `conversation_service.py`
3. 重组课程材料服务
4. 集成测试

### 阶段三：API 更新

1. 创建新的 API 路由
2. 标记旧 API 为 deprecated
3. 更新前端调用
4. API 测试

### 阶段四：部署验证

1. 环境变量配置测试
2. 完整功能验证
3. 性能测试
4. 文档更新

## ⚠️ 注意事项

1. **数据兼容性**：确保 Redis 和 Qdrant 数据格式兼容
2. **API 兼容性**：保持现有 API 的响应格式不变
3. **配置迁移**：提供配置迁移指南
4. **监控告警**：添加服务健康监控
5. **回滚方案**：准备快速回滚到原有架构的方案

---

**重构原则：一边改一边测试。宁愿慢，不愿错。**
